var s="messageLintRule.inlang.camelCaseId";var i={en:"Camel case id"},n={en:"Checks for messages to have a camel case formatted message id (e.g. 'myMessage')."};var l=e=>/^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/.test(e);var t={id:s,displayName:i,description:n,run:({message:e,settings:m,report:r})=>{if(!l(e.id))return r({messageId:e.id,languageTag:m.sourceLanguageTag,body:{en:`Message with id '${e.id}' should be in camel case format.`}})}};var h=t;export{h as default};
