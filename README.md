# Albion profit

![](https://img.shields.io/badge/up--npm-%20?style=flat&logo=rocket&logoColor=rgb(56%2C%20167%2C%20205)&label=updated%20with&color=rgb(74%2C%20100%2C%20206)&link=https%3A%2F%2Fgithub.com%2FIcaruk%2Fup-npm)

[Albion Profit](https://icaruk.github.io/albion-profit/) is a tool that enables users to semi-automatically calculate the profit of any combination of one product with multiple ingredients. 

![3zmvCg6](https://github.com/user-attachments/assets/22761220-9fc7-438c-a001-84ec4b5ead13)

Shopping list  
<img src="https://i.imgur.com/gTDmK2h.png" alt="shopping list screenshot" width="500"/>

# Why

- ⚡ Easiest way to calculate if an item craft is profitable.
- 🚀 Better and faster than google sheets / excel.
- ♾️ You can make endless groups with endless components.
- 🪄 It fetches prices for all the items inside each group, but it works without it.
- 💾 Save your calculations for later, all data is saved on your browser.

# Features

- 📚 All servers supported.
- 🌐 Multilanguage: ![](https://flagsapi.com/GB/flat/16.png) ![](https://flagsapi.com/ES/flat/16.png) ![](https://flagsapi.com/FR/flat/16.png).
- 🔨 Automatically add crafting ingredients based on the selected recipe.
- 🔄 Automatically get prices.
- 🛒 Shopping list for buying all ingredients at once.
- 📋 Click on any item icon to copy its name to your clipboard.
- 💰 Switch between sell price or buy price.
- ➕ Automatically increment the quantity of each ingredient to match the quantity of the main item.
- 📈 Visualize market prices and sold item count for each quality.
- 🎯 Calculate the best selling quality.
- 📦 Change order of groups for better organization.

# Missing data?

- Contribute to Albion Online Data Project using [this](https://www.albion-online-data.com/#player-information). Just run the executable and play Albion, it will automatically collect the market orders that you load in game.

---

# Known issues

- https://github.com/Icaruk/albion-profit/issues/4
- https://github.com/Icaruk/albion-profit/issues/8

# TODO

- [ ] Detect tier writing "T4.0" on the search [using this](https://mantine.dev/core/select/#options-filtering)


# Made with

- [React](https://react.dev) + [Vite](https://vitejs.dev)
- [Mantine](https://mantine.dev)
- [Inlang](https://inlang.com)
- [The Albion Online Data Project](https://www.albion-online-data.com)
