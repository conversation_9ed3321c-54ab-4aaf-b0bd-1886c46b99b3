/* eslint-disable */
import { languageTag } from "./runtime.js"
import * as en from "./messages/en.js"
import * as es from "./messages/es.js"
import * as fr from "./messages/fr.js"


/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [<PERSON> (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const addComponent = (params = {}, options = {}) => {
	return {
		en: en.addComponent,
		es: es.addComponent,
		fr: fr.addComponent
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const fetchPrices = (params = {}, options = {}) => {
	return {
		en: en.fetchPrices,
		es: es.fetchPrices,
		fr: fr.fetchPrices
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const addGroup = (params = {}, options = {}) => {
	return {
		en: en.addGroup,
		es: es.addGroup,
		fr: fr.addGroup
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const resultValue = (params = {}, options = {}) => {
	return {
		en: en.resultValue,
		es: es.resultValue,
		fr: fr.resultValue
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const cost = (params = {}, options = {}) => {
	return {
		en: en.cost,
		es: es.cost,
		fr: fr.cost
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const earnings = (params = {}, options = {}) => {
	return {
		en: en.earnings,
		es: es.earnings,
		fr: fr.earnings
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const quantity = (params = {}, options = {}) => {
	return {
		en: en.quantity,
		es: es.quantity,
		fr: fr.quantity
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const price = (params = {}, options = {}) => {
	return {
		en: en.price,
		es: es.price,
		fr: fr.price
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const total = (params = {}, options = {}) => {
	return {
		en: en.total,
		es: es.total,
		fr: fr.total
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const group = (params = {}, options = {}) => {
	return {
		en: en.group,
		es: es.group,
		fr: fr.group
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const result = (params = {}, options = {}) => {
	return {
		en: en.result,
		es: es.result,
		fr: fr.result
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const components = (params = {}, options = {}) => {
	return {
		en: en.components,
		es: es.components,
		fr: fr.components
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const noTax = (params = {}, options = {}) => {
	return {
		en: en.noTax,
		es: es.noTax,
		fr: fr.noTax
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{ num: NonNullable<unknown> }} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellWithPremium = (params , options = {}) => {
	return {
		en: en.sellWithPremium,
		es: es.sellWithPremium,
		fr: fr.sellWithPremium
	}[options.languageTag ?? languageTag()](params)
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{ num: NonNullable<unknown> }} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellWithoutPremium = (params , options = {}) => {
	return {
		en: en.sellWithoutPremium,
		es: es.sellWithoutPremium,
		fr: fr.sellWithoutPremium
	}[options.languageTag ?? languageTag()](params)
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{ num: NonNullable<unknown> }} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellOrderWithPremium = (params , options = {}) => {
	return {
		en: en.sellOrderWithPremium,
		es: es.sellOrderWithPremium,
		fr: fr.sellOrderWithPremium
	}[options.languageTag ?? languageTag()](params)
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{ num: NonNullable<unknown> }} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellOrderWithoutPremium = (params , options = {}) => {
	return {
		en: en.sellOrderWithoutPremium,
		es: es.sellOrderWithoutPremium,
		fr: fr.sellOrderWithoutPremium
	}[options.languageTag ?? languageTag()](params)
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const earningsAfterTax = (params = {}, options = {}) => {
	return {
		en: en.earningsAfterTax,
		es: es.earningsAfterTax,
		fr: fr.earningsAfterTax
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const returnRateTooltip = (params = {}, options = {}) => {
	return {
		en: en.returnRateTooltip,
		es: es.returnRateTooltip,
		fr: fr.returnRateTooltip
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const bindQuantityTooltip = (params = {}, options = {}) => {
	return {
		en: en.bindQuantityTooltip,
		es: es.bindQuantityTooltip,
		fr: fr.bindQuantityTooltip
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const bindQuantity = (params = {}, options = {}) => {
	return {
		en: en.bindQuantity,
		es: es.bindQuantity,
		fr: fr.bindQuantity
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellOrderPriceChangeCountBeforeLoss = (params = {}, options = {}) => {
	return {
		en: en.sellOrderPriceChangeCountBeforeLoss,
		es: es.sellOrderPriceChangeCountBeforeLoss,
		fr: fr.sellOrderPriceChangeCountBeforeLoss
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const priceIsLockedTooltip = (params = {}, options = {}) => {
	return {
		en: en.priceIsLockedTooltip,
		es: es.priceIsLockedTooltip,
		fr: fr.priceIsLockedTooltip
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const shoppingList = (params = {}, options = {}) => {
	return {
		en: en.shoppingList,
		es: es.shoppingList,
		fr: fr.shoppingList
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const removeAll = (params = {}, options = {}) => {
	return {
		en: en.removeAll,
		es: es.removeAll,
		fr: fr.removeAll
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const emptyShoppingListHint = (params = {}, options = {}) => {
	return {
		en: en.emptyShoppingListHint,
		es: es.emptyShoppingListHint,
		fr: fr.emptyShoppingListHint
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const getComponents = (params = {}, options = {}) => {
	return {
		en: en.getComponents,
		es: es.getComponents,
		fr: fr.getComponents
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const deleteThisComponent = (params = {}, options = {}) => {
	return {
		en: en.deleteThisComponent,
		es: es.deleteThisComponent,
		fr: fr.deleteThisComponent
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const removeFromShoppingList = (params = {}, options = {}) => {
	return {
		en: en.removeFromShoppingList,
		es: es.removeFromShoppingList,
		fr: fr.removeFromShoppingList
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const addToShoppingList = (params = {}, options = {}) => {
	return {
		en: en.addToShoppingList,
		es: es.addToShoppingList,
		fr: fr.addToShoppingList
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const buyPrice = (params = {}, options = {}) => {
	return {
		en: en.buyPrice,
		es: es.buyPrice,
		fr: fr.buyPrice
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const sellPrice = (params = {}, options = {}) => {
	return {
		en: en.sellPrice,
		es: es.sellPrice,
		fr: fr.sellPrice
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const priceModeSwitchTooltip = (params = {}, options = {}) => {
	return {
		en: en.priceModeSwitchTooltip,
		es: es.priceModeSwitchTooltip,
		fr: fr.priceModeSwitchTooltip
	}[options.languageTag ?? languageTag()]()
}



/**
 * This message has been compiled by [inlang paraglide](https://inlang.com/m/gerre34r/library-inlang-paraglideJs).
 *
 * - Don't edit the message's code. Use [Sherlock (VS Code extension)](https://inlang.com/m/r7kp499g/app-inlang-ideExtension),
 *   the [web editor](https://inlang.com/m/tdozzpar/app-inlang-finkLocalizationEditor) instead, or edit the translation files manually.
 * 
 * - The params are NonNullable<unknown> because the inlang SDK does not provide information on the type of a param (yet).
 * 
 * @param {{}} params
 * @param {{ languageTag?: "en" | "es" | "fr" }} options
 * @returns {string}
 */
/* @__NO_SIDE_EFFECTS__ */
export const itemData = (params = {}, options = {}) => {
	return {
		en: en.itemData,
		es: es.itemData,
		fr: fr.itemData
	}[options.languageTag ?? languageTag()]()
}

