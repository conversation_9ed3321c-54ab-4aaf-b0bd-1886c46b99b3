.mainContainer {
	height: 100vh;
	overflow: hidden;

	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.image {
	position: absolute;
	height: 100vh;
	object-fit: cover;
	filter: blur(8px);
	z-index: -1;
}

.leftContainer {
	position: fixed;

	height: 100vh;
	width: 450px;
}

.leftContainerItem {
	filter: brightness(1);
}
.leftContainerItem:hover {
	filter: brightness(1.1);
}

.dataContainer {
	margin-left: 450px;
	padding-left: 16px;
}
